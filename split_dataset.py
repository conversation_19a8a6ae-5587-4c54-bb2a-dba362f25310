import os
import random
import shutil
from pathlib import Path

def split_dataset(images_dir, labels_dir, output_dir, train_ratio=0.7, seed=42):
    """
    将大幅遥感图像数据集按指定比例分割为训练集和验证集
    
    Args:
        images_dir: 原始图像目录路径
        labels_dir: 标签图像目录路径  
        output_dir: 输出目录路径
        train_ratio: 训练集比例(默认0.7)
        seed: 随机种子
    """
    random.seed(seed)
    
    # 创建输出目录
    train_images_dir = os.path.join(output_dir, 'train', 'images')
    train_labels_dir = os.path.join(output_dir, 'train', 'labels')
    val_images_dir = os.path.join(output_dir, 'val', 'images')
    val_labels_dir = os.path.join(output_dir, 'val', 'labels')
    
    os.makedirs(train_images_dir, exist_ok=True)
    os.makedirs(train_labels_dir, exist_ok=True)
    os.makedirs(val_images_dir, exist_ok=True)
    os.makedirs(val_labels_dir, exist_ok=True)
    
    # 获取图像文件列表
    image_files = [f for f in os.listdir(images_dir) 
                  if f.lower().endswith(('.tif', '.tiff', '.png', '.jpg', '.jpeg'))]
    
    # 确保图像和标签文件对应
    valid_pairs = []
    for img_file in image_files:
        label_file = img_file.replace('_img_', '_label_')
        
        if os.path.exists(os.path.join(labels_dir, label_file)):
            valid_pairs.append((img_file, label_file))
        else:
            print(f"警告: 未找到图像 {img_file} 对应的标签文件 {label_file}")
    
    print(f"找到 {len(valid_pairs)} 对有效的图像-标签文件")
    
    if len(valid_pairs) == 0:
        print("错误: 没有找到任何有效的图像-标签对")
        return
    
    # 随机打乱数据
    random.shuffle(valid_pairs)
    
    # 计算分割点
    split_point = int(len(valid_pairs) * train_ratio)
    train_pairs = valid_pairs[:split_point]
    val_pairs = valid_pairs[split_point:]
    
    print(f"训练集: {len(train_pairs)} 对")
    print(f"验证集: {len(val_pairs)} 对")
    
    # 复制文件到训练集
    for img_file, label_file in train_pairs:
        shutil.copy2(os.path.join(images_dir, img_file), 
                    os.path.join(train_images_dir, img_file))
        shutil.copy2(os.path.join(labels_dir, label_file), 
                    os.path.join(train_labels_dir, label_file))
    
    # 复制文件到验证集
    for img_file, label_file in val_pairs:
        shutil.copy2(os.path.join(images_dir, img_file), 
                    os.path.join(val_images_dir, img_file))
        shutil.copy2(os.path.join(labels_dir, label_file), 
                    os.path.join(val_labels_dir, label_file))
    
    print("数据集分割完成！")

if __name__ == "__main__":
    # 设置路径
    base_dir = "data/AerialImageDataset/cropped"
    images_dir = os.path.join(base_dir,  "images")
    labels_dir = os.path.join(base_dir, "labels")
    output_dir = os.path.join(base_dir, "split")
    
    # 执行分割
    split_dataset(images_dir, labels_dir, output_dir, train_ratio=0.7)