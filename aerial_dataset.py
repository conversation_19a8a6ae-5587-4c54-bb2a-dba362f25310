import os
from PIL import Image
from torch.utils.data import Dataset
import numpy as np


class AerialImageDataset(Dataset):
    def __init__(self, image_dir, mask_dir, transform=None):
        self.image_dir = image_dir
        self.mask_dir = mask_dir
        self.transform = transform
        self.images = [f for f in os.listdir(image_dir) 
                      if f.lower().endswith(('.tif', '.tiff', '.png', '.jpg', '.jpeg'))]

    def __len__(self):
        return len(self.images)

    def __getitem__(self, index):
        img_name = self.images[index]
        img_path = os.path.join(self.image_dir, img_name)
        
        # 根据图像文件名生成对应的mask文件名
        base_name = os.path.splitext(img_name)[0]
        mask_filename = base_name.replace('_img_', '_label_') + '.tif'
        mask_path = os.path.join(self.mask_dir, mask_filename)
        
        # 如果tif文件不存在，尝试其他格式
        if not os.path.exists(mask_path):
            for ext in ['.png', '.jpg', '.jpeg']:
                alt_mask_path = os.path.join(self.mask_dir, base_name.replace('_img_', '_label_') + ext)
                if os.path.exists(alt_mask_path):
                    mask_path = alt_mask_path
                    break
        
        image = np.array(Image.open(img_path).convert('RGB'))
        mask = np.array(Image.open(mask_path).convert("L"), dtype=np.float32)
        mask[mask == 255.0] = 1.0  # 二值化

        if self.transform is not None:
            augmentations = self.transform(image=image, mask=mask)
            image = augmentations['image']
            mask = augmentations['mask']

        return image, mask


if __name__ == "__main__":
    # 测试数据集
    dataset = AerialImageDataset(
        image_dir="dataset/AerialImageDataset/train/images",
        mask_dir="dataset/AerialImageDataset/train/labels"
    )
    print(f"数据集大小: {len(dataset)}")
    if len(dataset) > 0:
        image, mask = dataset[0]
        print(f"图像形状: {image.shape}, 掩码形状: {mask.shape}")