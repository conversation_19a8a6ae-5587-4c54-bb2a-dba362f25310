import os
import numpy as np
from PIL import Image
import rasterio
from rasterio.windows import Window
import argparse
from pathlib import Path

class ImageCropper:
    def __init__(self, tile_size=512, overlap=0):
        """
        初始化图像切割器
        
        Args:
            tile_size: 切割后的小图像尺寸
            overlap: 重叠像素数
        """
        self.tile_size = tile_size
        self.overlap = overlap
        self.stride = tile_size - overlap
    
    def crop_image_rasterio(self, image_path, label_path, output_dir, prefix=""):
        """
        使用rasterio切割大幅遥感影像（支持GeoTIFF）
        
        Args:
            image_path: 原始图像路径
            label_path: 标签图像路径
            output_dir: 输出目录
            prefix: 文件前缀
        """
        with rasterio.open(image_path) as src_img, rasterio.open(label_path) as src_label:
            height, width = src_img.height, src_img.width
            
            # 计算切割数量
            rows = (height - self.overlap) // self.stride
            cols = (width - self.overlap) // self.stride
            
            print(f"原始图像尺寸: {width}x{height}")
            print(f"切割数量: {rows}行 x {cols}列 = {rows * cols}个小图像")
            
            count = 0
            for row in range(rows):
                for col in range(cols):
                    # 计算窗口位置
                    y = row * self.stride
                    x = col * self.stride
                    
                    # 确保不超出边界
                    if y + self.tile_size > height:
                        y = height - self.tile_size
                    if x + self.tile_size > width:
                        x = width - self.tile_size
                    
                    # 创建窗口
                    window = Window(x, y, self.tile_size, self.tile_size)
                    
                    # 读取图像数据
                    img_data = src_img.read(window=window)
                    label_data = src_label.read(window=window)
                    
                    # 检查标签是否有效（避免全黑图像）
                    if np.sum(label_data) == 0:
                        continue
                    
                    # 保存切割后的图像
                    img_filename = f"{prefix}_img_{count:04d}.tif"
                    label_filename = f"{prefix}_label_{count:04d}.tif"
                    
                    img_path = os.path.join(output_dir, "images", img_filename)
                    label_path_out = os.path.join(output_dir, "labels", label_filename)
                    
                    # 保存图像
                    self.save_geotiff(img_data, img_path, src_img.profile, window)
                    self.save_geotiff(label_data, label_path_out, src_label.profile, window)
                    
                    count += 1
            
            print(f"成功保存 {count} 对图像和标签")
    
    def crop_image_pil(self, image_path, label_path, output_dir, prefix=""):
        """
        使用PIL切割普通图像（支持JPG/PNG等）
        
        Args:
            image_path: 原始图像路径
            label_path: 标签图像路径
            output_dir: 输出目录
            prefix: 文件前缀
        """
        # 打开图像
        image = Image.open(image_path)
        label = Image.open(label_path)
        
        # 确保图像和标签尺寸一致
        if image.size != label.size:
            raise ValueError("图像和标签尺寸不一致")
        
        width, height = image.size
        
        # 计算切割数量
        rows = (height - self.overlap) // self.stride
        cols = (width - self.overlap) // self.stride
        
        print(f"原始图像尺寸: {width}x{height}")
        print(f"切割数量: {rows}行 x {cols}列 = {rows * cols}个小图像")
        
        count = 0
        for row in range(rows):
            for col in range(cols):
                # 计算切割位置
                left = col * self.stride
                upper = row * self.stride
                right = left + self.tile_size
                lower = upper + self.tile_size
                
                # 确保不超出边界
                if right > width:
                    left = width - self.tile_size
                    right = width
                if lower > height:
                    upper = height - self.tile_size
                    lower = height
                
                # 切割图像
                img_crop = image.crop((left, upper, right, lower))
                label_crop = label.crop((left, upper, right, lower))
                
                # 检查标签是否有效
                if self.is_valid_label(label_crop):
                    # 保存切割后的图像
                    img_filename = f"{prefix}_img_{count:04d}.png"
                    label_filename = f"{prefix}_label_{count:04d}.png"
                    
                    img_path = os.path.join(output_dir, "images", img_filename)
                    label_path_out = os.path.join(output_dir, "labels", label_filename)
                    
                    img_crop.save(img_path)
                    label_crop.save(label_path_out)
                    
                    count += 1
        
        print(f"成功保存 {count} 对图像和标签")
    
    def is_valid_label(self, label_img):
        """检查标签是否有效（非全黑）"""
        label_array = np.array(label_img)
        return np.sum(label_array) > 0
    
    def save_geotiff(self, data, filepath, profile, window):
        """保存GeoTIFF文件"""
        profile.update({
            'height': self.tile_size,
            'width': self.tile_size,
            'transform': rasterio.windows.transform(window, profile['transform'])
        })
        
        with rasterio.open(filepath, 'w', **profile) as dst:
            dst.write(data)
    
    def process_directory(self, images_dir, labels_dir, output_dir, tile_size=512, overlap=0, file_type="tif"):
        """
        批量处理目录中的所有图像
        
        Args:
            images_dir: 原始图像目录
            labels_dir: 标签图像目录
            output_dir: 输出目录
            tile_size: 切割尺寸
            overlap: 重叠像素
            file_type: 文件类型 (tif, png, jpg)
        """
        self.tile_size = tile_size
        self.overlap = overlap
        self.stride = tile_size - overlap
        
        # 创建输出目录
        os.makedirs(os.path.join(output_dir, "images"), exist_ok=True)
        os.makedirs(os.path.join(output_dir, "labels"), exist_ok=True)
        
        # 获取文件列表
        image_files = [f for f in os.listdir(images_dir) 
                      if f.lower().endswith(('.tif', '.tiff', '.png', '.jpg', '.jpeg'))]
        
        print(f"找到 {len(image_files)} 个图像文件")
        
        for idx, filename in enumerate(image_files):
            print(f"\n处理文件 {idx+1}/{len(image_files)}: {filename}")
            
            image_path = os.path.join(images_dir, filename)
            label_filename = filename  # 假设标签文件同名
            label_path = os.path.join(labels_dir, label_filename)
            
            if not os.path.exists(label_path):
                print(f"警告: 未找到对应的标签文件 {label_filename}")
                continue
            
            prefix = Path(filename).stem
            
            try:
                if file_type.lower() in ['tif', 'tiff']:
                    self.crop_image_rasterio(image_path, label_path, output_dir, prefix)
                else:
                    self.crop_image_pil(image_path, label_path, output_dir, prefix)
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {str(e)}")

def main():
    # 使用示例
    cropper = ImageCropper(tile_size=512, overlap=64)  # 512x512像素，64像素重叠
    
    # 设置路径
    images_dir = r"data\AerialImageDataset\train\images"
    labels_dir = r"data\AerialImageDataset\train\gt"
    output_dir = r"data\AerialImageDataset\cropped"
    
    # 批量处理
    cropper.process_directory(
        images_dir=images_dir,
        labels_dir=labels_dir,
        output_dir=output_dir,
        tile_size=512,
        overlap=64,
        file_type="tif"
    )

if __name__ == "__main__":
    main()