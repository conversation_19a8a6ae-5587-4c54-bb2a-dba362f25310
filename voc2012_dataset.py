import os
from PIL import Image
from torch.utils.data import Dataset
import numpy as np


class VOC2012Dataset(Dataset):
    def __init__(self, image_dir, mask_dir, split_file=None, transform=None):
        self.image_dir = image_dir
        self.mask_dir = mask_dir
        self.transform = transform
        
        if split_file:
            with open(split_file) as f:
                self.image_names = [line.strip() + '.jpg' for line in f]
        else:
            self.image_names = [f for f in os.listdir(image_dir) if f.endswith('.jpg')]

    def __len__(self):
        return len(self.image_names)

    def __getitem__(self, index):
        img_path = os.path.join(self.image_dir, self.image_names[index])
        mask_path = os.path.join(self.mask_dir, 
                               self.image_names[index].replace('.jpg', '.png'))

        image = np.array(Image.open(img_path).convert('RGB'))
        mask = np.array(Image.open(mask_path), dtype=np.float32)
        
        # Normalize mask to 0-1 range if needed
        if mask.max() > 1:
            mask = mask / 255.0

        if self.transform is not None:
            augmentations = self.transform(image=image, mask=mask)
            image = augmentations['image']
            mask = augmentations['mask']

        return image, mask
