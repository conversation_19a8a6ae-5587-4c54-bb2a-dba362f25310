import torch
import torchvision
from aerial_dataset import AerialImageDataset
from carvana_dataset import CarvanaDataset
from torch.utils.data import DataLoader
import logging


# 加载数据的参数函数
def get_loaders(dataset,train_dir,train_mask_dir,val_dir,val_mask_dir,batch_size,train_transform,val_transform,num_workers):
    if dataset=="carvana":
    # 加载训练集
        train_set = CarvanaDataset(image_dir=train_dir,mask_dir=train_mask_dir,transform=train_transform)
        train_loader = DataLoader(train_set,batch_size=batch_size,num_workers=num_workers,shuffle=True)

        # 加载验证集
        val_set = CarvanaDataset(image_dir=val_dir,mask_dir=val_mask_dir,transform=val_transform)
        val_loader = DataLoader(val_set,batch_size=batch_size,num_workers=num_workers,shuffle=False)

    elif dataset=="aerial":
        # 加载训练集
        train_set = AerialImageDataset(image_dir=train_dir,mask_dir=train_mask_dir,transform=train_transform)
        train_loader = DataLoader(train_set,batch_size=batch_size,num_workers=num_workers,shuffle=True)

        # 加载验证集
        val_set = AerialImageDataset(image_dir=val_dir,mask_dir=val_mask_dir,transform=val_transform)
        val_loader = DataLoader(val_set,batch_size=batch_size,num_workers=num_workers,shuffle=False)
    return train_loader,val_loader


# 检验精度
def check_accuracy(loader,model,device):
    num_correct = 0
    num_pixels = 0
    dice_score = 0

    model.eval()            # 测试模式
    with torch.no_grad():
        for x,y in loader:
            x = x.to(device)
            y = y.to(device).unsqueeze(1)   # add label 中的channel维度
            pred = torch.sigmoid(model(x))
            pred = (pred > 0.5 ).float()        # 转化为二值图像
            num_correct += (pred == y).sum()   # prediction 和 label中相同像素点的个数
            num_pixels += torch.numel(pred)        # 统计 y 中像素点的个数
            dice_score += ( 2*(pred * y).sum() ) / ((pred + y).sum() + 1e-8 )

    # 预测像素点正确的个数 / label
    logging.info(f'Got {num_correct}/{num_pixels} with accuracy {num_correct/num_pixels*100:.2f}%')
    # Dice 指标
    logging.info(f'Dice score : {dice_score / len(loader)}')

    model.train()


# show 预测图片
def save_predictions_as_imgs(loader,model,device,folder = './saved_val_images/'):
    print('------>Loading predictions')
    model.eval()
    for idx,(x,y) in enumerate(loader):
        x = x.to(device=device)
        with torch.no_grad():
            pred = torch.sigmoid(model(x))
            pred = (pred > 0.5).float()

        torchvision.utils.save_image(pred, f'{folder}/pred_{idx}.png')              # 保存预测图像
        torchvision.utils.save_image(y.unsqueeze(1),f'{folder}/label_{idx}.png')    # 保存label图像

    model.train()
