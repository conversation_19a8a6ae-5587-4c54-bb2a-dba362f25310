import os
os.environ['KMP_DUPLICATE_LIB_OK']='True'

import torch
import albumentations as A
from albumentations.pytorch import ToTensorV2
from tqdm import tqdm
import torch.nn as nn
from unet import UNet
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import StepLR
import logging
import datetime

from utils import (
    check_accuracy,
    save_predictions_as_imgs,
)
from voc2012_dataset import VOC2012Dataset

# Hyperparameters
LEARNING_RATE = 1e-6
DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'
BATCH_SIZE = 16
NUM_EPOCHS = 20
NUM_WORKERS = 5
#IMAGE_HEIGHT = 160
#IMAGE_WIDTH = 240

IMAGE_HEIGHT = 384
IMAGE_WIDTH = 512

#LOAD_MODEL = False
LOAD_MODEL = True

# VOC2012 dataset paths
TRAIN_IMG_DIR = 'data/datasets/VOCdevkit/VOC2012/JPEGImages'
TRAIN_MASK_DIR = 'data/datasets/VOCdevkit/VOC2012/SegmentationClass'
TRAIN_SPLIT_FILE = 'data/datasets/VOCdevkit/VOC2012/ImageSets/Segmentation/train.txt'
VAL_SPLIT_FILE = 'data/datasets/VOCdevkit/VOC2012/ImageSets/Segmentation/val.txt'

def train_fn(loader, model, optimizer, loss_fn, scaler):
    loop = tqdm(loader)
    for batch_idx, (img, label) in enumerate(loop):
        img = img.to(device=DEVICE)
        label = label.float().unsqueeze(1).to(DEVICE)

        with torch.cuda.amp.autocast():
            predictions = model(img)
            loss = loss_fn(predictions, label)

        optimizer.zero_grad()
        scaler.scale(loss).backward()
        scaler.step(optimizer)
        scaler.update()

        loop.set_postfix(loss=loss.item())

def main():
    train_transforms = A.Compose([
        A.Resize(height=IMAGE_HEIGHT, width=IMAGE_WIDTH),
        A.Rotate(limit=35, p=0.5),
        A.HorizontalFlip(p=0.5),
        A.VerticalFlip(p=0.1),
        A.Normalize(
            mean=[0.0, 0.0, 0.0],
            std=[1.0, 1.0, 1.0],
            max_pixel_value=255.0
        ),
        ToTensorV2(),
    ])

    val_transforms = A.Compose([
        A.Resize(height=IMAGE_HEIGHT, width=IMAGE_WIDTH),
        A.Normalize(
            mean=[0.0, 0.0, 0.0],
            std=[1.0, 1.0, 1.0],
            max_pixel_value=255.0
        ),
        ToTensorV2(),
    ])

    # Create datasets
    train_ds = VOC2012Dataset(
        image_dir=TRAIN_IMG_DIR,
        mask_dir=TRAIN_MASK_DIR,
        split_file=TRAIN_SPLIT_FILE,
        transform=train_transforms
    )
    val_ds = VOC2012Dataset(
        image_dir=TRAIN_IMG_DIR,
        mask_dir=TRAIN_MASK_DIR,
        split_file=VAL_SPLIT_FILE,
        transform=val_transforms
    )

    # Create data loaders
    train_loader = DataLoader(
        train_ds,
        batch_size=BATCH_SIZE,
        num_workers=NUM_WORKERS,
        shuffle=True
    )
    val_loader = DataLoader(
        val_ds,
        batch_size=BATCH_SIZE,
        num_workers=NUM_WORKERS,
        shuffle=False
    )

    model = UNet(in_channels=3, out_channels=1).to(DEVICE)
    loss_fn = nn.BCEWithLogitsLoss()
    optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE)
    scheduler = StepLR(optimizer, step_size=30, gamma=0.1)
    scaler = torch.cuda.amp.GradScaler()

    if LOAD_MODEL:
        model.load_state_dict(torch.load('pretrained-unet.pth'))
        check_accuracy(val_loader, model, device=DEVICE)

    for epoch in range(NUM_EPOCHS):
        logging.info(f'Epoch: {epoch + 1}')
        train_fn(train_loader, model, optimizer, loss_fn, scaler)
        scheduler.step()
        check_accuracy(val_loader, model, device=DEVICE)

    torch.save(model.state_dict(), 'unet_voc2012.pth')
    save_predictions_as_imgs(val_loader, model, folder='saved_val_images/', device=DEVICE)

if __name__ == '__main__':
    # Configure logging
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'voc2012_train_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
            logging.StreamHandler()
        ]
    )
    logging.debug('Starting training...')
    logging.info(f'Using device: {DEVICE}')
    logging.info(f'Batch size: {BATCH_SIZE}')
    logging.info(f'Image size: {IMAGE_HEIGHT}x{IMAGE_WIDTH}')

    # Test file writing
    try:
        with open('test_log_permission.log', 'w') as f:
            f.write('Test log file writing\n')
    except Exception as e:
        logging.error(f'File write test failed: {e}')

    main()
    logging.info('Training complete!')

    # Ensure handlers are properly closed at the very end
    for handler in logging.root.handlers[:]:
        handler.close()
        logging.root.removeHandler(handler)
